CREATE OR REPLACE PROCEDURE irap_02_01_PRO_102 (cdate in date)
is
   pDate        varchar2(15);  -- variable for Process Date
   --vExistsHLR   number;        -- variable to check HLR
   vExistsInnate   varchar2(10);  -- variable to check MTX
   vExistsRPOS   varchar2(10);  -- variable to check RPOS
   vExists      varchar2(10);  -- this flag is used for Exceptions
   flag         varchar2(25);  -- variable to check flag for exceptions
   alarmID      VARCHAR2(25);  -- this is variable for Alarm ID

begin


  /*---------------------------------------------------------------------------------------
                Order Management and Provisioning - tmForum Module 1
                        RAP - Module 2.0 - Provisioning
                            Last Update: June-2025

     This procedure Runs Daily from a Crontab in KBLRAMFM/RAAGENT/Perl script Linux System
     and Reconciles provisioning of MSISDN with HLR and Billing system(s) MTX/CBS.
     The Summary of the reconciliation will be updated in this RA database forever.
     The RAP Web application uses the Summary Table for this module to present the data.

  ----------------------------------------------------------------------------------------*/

   -- Format the ProcessDate in DateKey
   pDate := to_char(cdate,'YYYYMMDD');

   -- Create temporary tables for performance optimization
   -- Drop temporary tables if they exist (cleanup from previous runs)
   BEGIN
      EXECUTE IMMEDIATE 'DROP TABLE tmp_innate_dmp';
   EXCEPTION
      WHEN OTHERS THEN NULL;
   END;

   BEGIN
      EXECUTE IMMEDIATE 'DROP TABLE tmp_rpos';
   EXCEPTION
      WHEN OTHERS THEN NULL;
   END;

   -- Create temporary table for INNATE data
   EXECUTE IMMEDIATE 'CREATE GLOBAL TEMPORARY TABLE tmp_innate_dmp (
      date_key VARCHAR2(15),
      msisdn VARCHAR2(20),
      imsi VARCHAR2(20),
      status NUMBER
   ) ON COMMIT PRESERVE ROWS';

   -- Create temporary table for RPOS data
   EXECUTE IMMEDIATE 'CREATE GLOBAL TEMPORARY TABLE tmp_rpos (
      msisdn VARCHAR2(20),
      status VARCHAR2(20),
      identification_number VARCHAR2(50),
      visa_number VARCHAR2(50),
      visa_expiration DATE,
      applicant_type VARCHAR2(50),
      identification_type VARCHAR2(50),
      gaurantor_doc_number VARCHAR2(50)
   ) ON COMMIT PRESERVE ROWS';

   -- Load data into temporary tables
   INSERT INTO tmp_innate_dmp
   SELECT date_key, msisdn, imsi, status
   FROM redf.innate_dmp@dred
   WHERE date_key = pDate;

   INSERT INTO tmp_rpos
   SELECT msisdn, status, identification_number, visa_number, visa_expiration,
          applicant_type, identification_type, gaurantor_doc_number
   FROM <EMAIL>;

   COMMIT;

   -- Check if Dumps are loaded in DW using temporary tables
   SELECT count(*) into vExistsInnate from tmp_innate_dmp;
   SELECT count(*) into vExistsRPOS FROM tmp_rpos;

   -- Check if the Dumps have enough records 6.m loaded or not
   if vExistsInnate > 3000000 and vExistsRPOS > 3000000 then

      -----------------------------------------------------------------
      -- Step 1 - Download the Data and DataMart
      -----------------------------------------------------------------
      -- Truncate the HLR table
      execute immediate 'truncate table irap_02_04_pro_INNATE_vs_RPOS';


      -- Create the Data Mart of Reconciliation using temporary tables
        INSERT INTO irap_02_04_pro_INNATE_vs_RPOS
        select
        A.RPOS_MSISDN,
        A.RPOS_STATUS,
        B.DATE_KEY,
        B.INNATE_MSISDN,
        B.INNATE_IMSI,
        B.INNAT_STATUS,
        A.ID_LEN,
        A.VISA_NUM,
        A.VISA_EXPIRY,
        A.APPLICANT_TYPE,
        a.id_type,
        a.gaurantor
         from
          (
            select
               msisdn rpos_msisdn, status rpos_status,
               length(IDENTIFICATION_NUMBER) id_len,
               VISA_NUMBER VISA_NUM,
               VISA_EXPIRATION VISA_EXPIRY,
               APPLICANT_TYPE,
               IDENTIFICATION_TYPE id_type, GAURANTOR_DOC_NUMBER gaurantor
            from tmp_rpos

          ) a
          full outer join
          ( select
               date_key, msisdn innate_msisdn, imsi innate_IMSI, status innat_status
            from tmp_innate_dmp) b
            on a.rpos_msisdn = b.innate_msisdn;
            COMMIT;

      -----------------------------------------------------------------
      --1  Activated in RPOS, But IDL in CRM
      -----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status = 'activated' and INNAT_STATUS = 0
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 1, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
           select
              date_key, 1, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )'; 

          commit;

       End if;

       ----------------------------------------------------------------
      --2  Approved in RPOS, But IDL in CRM
      -----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status = 'approved' and INNAT_STATUS = 0
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 2, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
           select
              date_key, 2, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )'; 

          commit;

        End if;


      ----------------------------------------------------------------
      --3  Activated and Approved in RPOS, not exist in CRM
      ----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status IN('approved','Activated') --and INNAT_STATUS = 0
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 3, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
           select
              date_key, 3, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )';

          commit;

        End if;


      ----------------------------------------------------------------
      --4  Activated in RPOS, But Blocked in CRM
      ----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status = 'Activated' and INNAT_STATUS = 2
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 4, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
            select
              date_key, 4, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )';

          commit;

        End if;


       ----------------------------------------------------------------
       --5  Approved in RPOS, But Blocked in CRM
       ----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status = 'approved' and INNAT_STATUS = 2
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 5, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
           select
              date_key, 5, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )';

          commit;

        End if;


       ----------------------------------------------------------------
       --6  Accepted, Resubmit and Submit in RPOS, but active in CRM
       ----------------------------------------------------------------
       select count(*) into vExists from
      (
          select
             RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS
          from irap_02_04_pro_INNATE_vs_RPOS
          WHERE DATE_KEY = pDate  and rpos_status in ('accepted','submitted','resubmitted') and INNAT_STATUS = 1
      );

      select max(alarm_id)+1 into alarmID from irap_00_02_ops_alarm;

      IF vExists >0 THEN
         flag :='Yes';
      ELSE
         flag :='No';
         vExists :=0;
      END IF;

      -- Update Alarm Table
      insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
             values (alarmID, 6, pDate, 'RA',1, 0, flag, vExists, 'CRM=RPOS', sysdate);
      commit;

      IF flag = 'Yes' THEN

          -- Update the Exception Table
          execute immediate

          'Insert into irap_00_03_ops_exception
           select
              date_key, 6, ' || alarmID || ', ''RA'', 0, ''CDR|CRM vs RPOS'', ''RPOS_MSISDN|'' || RPOS_MSISDN, ''RPOS_STATUS|'' || RPOS_STATUS, ''ID_TYPE|'' || ID_TYPE, ''GAURANTOR|'' || GAURANTOR
           from
           (
              select
                 RPOS_MSISDN, RPOS_STATUS, DATE_KEY, INNATE_MSISDN, INNATE_IMSI, INNAT_STATUS, ID_TYPE, GAURANTOR
              from irap_02_04_pro_INNATE_vs_RPOS
              where  DATE_KEY = ' || pDate || ' and rpos_status = ''activated'' and INNAT_STATUS = 0
           )';

          commit;

        End if;

     else
     -- Send alarm for no data load
     insert into irap_00_02_ops_alarm (Alarm_ID,Control_id,DateKey,A_Type,A_Severity,A_Status,A_exception,A_Count,A_Remarks,GDate)
     values ((select max(alarm_id)+1 from irap_00_02_ops_alarm), 0, pDate, 'RA',1, 0, 'No', 0, 'HLR or MTX Dump Not loaded', sysdate );
     commit;
  end if;

  -- Cleanup temporary tables
  BEGIN
     EXECUTE IMMEDIATE 'DROP TABLE tmp_innate_dmp';
  EXCEPTION
     WHEN OTHERS THEN NULL;
  END;

  BEGIN
     EXECUTE IMMEDIATE 'DROP TABLE tmp_rpos';
  EXCEPTION
     WHEN OTHERS THEN NULL;
  END;

end irap_02_01_PRO_102;
/
